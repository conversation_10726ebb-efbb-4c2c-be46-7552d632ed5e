{"name": "ai-rezume-analyzer", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@react-router/node": "^7.7.1", "@react-router/serve": "^7.7.1", "clsx": "^2.1.1", "isbot": "^5.1.27", "pdfjs-dist": "^5.4.54", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-router": "^7.7.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.8"}, "devDependencies": {"@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.4", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.3.7", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}}