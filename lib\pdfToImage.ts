// lib/pdfToImage.ts - Version corrigée

export interface PdfConversionResult {
  imageUrl: string;
  file: File | null;
  error?: string;
}

let pdfjsLib: any = null;
let isLoading = false;
let loadPromise: Promise<any> | null = null;

async function loadPdfJs(): Promise<any> {
  if (pdfjsLib) return pdfjsLib;
  if (loadPromise) return loadPromise;

  isLoading = true;
  
  try {
    // Utiliser directement le CDN pour éviter les conflits de version
    loadPromise = import("https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.min.mjs").then((lib) => {
      // Utiliser le worker de la même version
      lib.GlobalWorkerOptions.workerSrc = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.worker.min.mjs";
      
      pdfjsLib = lib;
      isLoading = false;
      console.log('PDF.js loaded successfully from CDN version 4.0.379');
      return lib;
    }).catch((error) => {
      console.error('Failed to load PDF.js from CDN:', error);
      isLoading = false;
      
      // Fallback vers une version plus stable
      return import("https://unpkg.com/pdfjs-dist@4.0.379/build/pdf.min.mjs").then((lib) => {
        lib.GlobalWorkerOptions.workerSrc = "https://unpkg.com/pdfjs-dist@4.0.379/build/pdf.worker.min.mjs";
        pdfjsLib = lib;
        console.log('PDF.js loaded from unpkg fallback');
        return lib;
      });
    });

    return loadPromise;
  } catch (error) {
    isLoading = false;
    loadPromise = null;
    throw error;
  }
}

export async function convertPdfToImage(
  file: File
): Promise<PdfConversionResult> {
  console.log('Starting PDF conversion for:', file.name, file.type, file.size);
  
  // Validation du fichier
  if (!file.type.includes('pdf') && !file.name.toLowerCase().endsWith('.pdf')) {
    return {
      imageUrl: "",
      file: null,
      error: "File is not a PDF",
    };
  }

  if (file.size === 0) {
    return {
      imageUrl: "",
      file: null,
      error: "PDF file is empty",
    };
  }

  if (file.size > 50 * 1024 * 1024) { // 50MB limit
    return {
      imageUrl: "",
      file: null,
      error: "PDF file is too large (max 50MB)",
    };
  }

  try {
    console.log('Loading PDF.js...');
    const lib = await loadPdfJs();
    
    if (!lib) {
      throw new Error('Failed to load PDF.js library');
    }

    console.log('Converting file to array buffer...');
    const arrayBuffer = await file.arrayBuffer();
    
    console.log('Loading PDF document...');
    const loadingTask = lib.getDocument({ 
      data: arrayBuffer,
      // Options supplémentaires pour améliorer la compatibilité
      verbosity: 0, // Réduire les logs
      // Supprimer les options cMapUrl qui peuvent causer des problèmes
    });
    
    const pdf = await loadingTask.promise;
    console.log(`PDF loaded successfully. Pages: ${pdf.numPages}`);
    
    console.log('Getting first page...');
    const page = await pdf.getPage(1);

    console.log('Setting up canvas...');
    const viewport = page.getViewport({ scale: 2 }); // Réduire la scale pour éviter les problèmes de mémoire
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");

    if (!context) {
      throw new Error('Failed to get canvas 2D context');
    }

    canvas.width = viewport.width;
    canvas.height = viewport.height;

    context.imageSmoothingEnabled = true;
    context.imageSmoothingQuality = "high";

    console.log('Rendering page to canvas...');
    const renderTask = page.render({ 
      canvasContext: context, 
      viewport: viewport,
    });
    
    await renderTask.promise;
    console.log('Page rendered successfully');

    return new Promise((resolve) => {
      console.log('Converting canvas to blob...');
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const originalName = file.name.replace(/\.pdf$/i, "");
            const imageFile = new File([blob], `${originalName}.png`, {
              type: "image/png",
            });

            console.log('PDF converted successfully to image');
            resolve({
              imageUrl: URL.createObjectURL(blob),
              file: imageFile,
            });
          } else {
            console.error('Failed to create blob from canvas');
            resolve({
              imageUrl: "",
              file: null,
              error: "Failed to create image blob from canvas",
            });
          }
        },
        "image/png",
        0.9 // Réduire la qualité pour éviter les fichiers trop gros
      );
    });
  } catch (err) {
    console.error('PDF conversion error:', err);
    
    let errorMessage = "Unknown error occurred";
    if (err instanceof Error) {
      errorMessage = err.message;
    } else if (typeof err === 'string') {
      errorMessage = err;
    }

    return {
      imageUrl: "",
      file: null,
      error: `Failed to convert PDF: ${errorMessage}`,
    };
  }
}