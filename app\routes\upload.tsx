import { useState, type FormEvent } from 'react'
import { FileUploader } from '~/components/FileUploader'
import Navbar from '~/components/Navbar'

const upload = () => {
    const [isProcessing, setIsProcessing] = useState(false)
    const [statusText , setStatusText] = useState('')
    const [file, setFile] = useState<File | null>(null)

    const handleFileSelect = (file: File | null) => {
        setFile(file);
      };

    const handleSubmit = (e: FormEvent<HTMLFormElement>) =>{
      e.preventDefault()
      const form = e.currentTarget.closest('form')
      if (!form) return
      const formData = new FormData(form)
      const compagnyName = formData.get('company-name') 
      const jobTitle = formData.get('job-title') 
      const jobDescription = formData.get('job-description') 
      console.log(compagnyName, jobTitle, jobDescription, file);
            
    }


  return (
    <main className="bg-[url('/images/bg-main.svg')] bg-cover">
      <Navbar />
      <section className="main-section ">
        <div className="page-heading py-16">
            <h1>Smart feedback for your dream job</h1>
            {isProcessing? (
                <>
                <h2>{statusText}</h2>
                <img src="/images/resume-scan.gif" className='w-full' alt="resume-scan-gif" />
                </>
            ) : (
                <h2>Drop your resume for an ATS score and improvement tips</h2>
            )}

            {!isProcessing && (
              <form id="upload-form" onSubmit={handleSubmit} className='flex flex-col gap-4 mt-8'>
                <div className="form-div">
                    <label htmlFor="company-name">Company Name</label>
                    <input type="text" name="company-name" id="company-name" placeholder='company name' className='w-full' />
                </div>
                <div className="form-div">
                    <label htmlFor="job-title">Job Title</label>
                    <input type="text" name="job-title" id="job-title" placeholder='job title' className='w-full' />
                </div>
                <div className="form-div">
                    <label htmlFor="job-description">Job Description</label>
                    <textarea rows={5} style={{resize: 'none'}} name="job-description" id="job-description" placeholder='job description' className='w-full' />
                </div>
                <div className="form-div">
                    <label htmlFor="uploader">Upload Resume</label>
                    <FileUploader onFileSelect={handleFileSelect} />
                </div>
                <button type="submit" className='primary-button '>Analyze Resume</button>
              </form>
            )}
        </div>
      </section>
    </main>
  )
}

export default upload