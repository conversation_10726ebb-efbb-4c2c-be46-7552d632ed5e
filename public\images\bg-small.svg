<svg width="925" height="1209" viewBox="0 0 925 1209" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_9323_560)">
<rect width="925" height="1209" fill="url(#paint0_linear_9323_560)"/>
<g opacity="0.35" filter="url(#filter0_f_9323_560)">
<ellipse cx="1100.5" cy="259.5" rx="384.5" ry="476.5" fill="#7C87FB"/>
</g>
<g opacity="0.35" filter="url(#filter1_f_9323_560)">
<ellipse cx="-84" cy="781.5" rx="357" ry="438.5" fill="#FA7185"/>
</g>
<rect x="777.827" y="297" width="234.11" height="527.655" transform="rotate(30 777.827 297)" fill="url(#paint1_linear_9323_560)"/>
<rect x="349.436" y="-10" width="234.112" height="578.872" transform="rotate(30 349.436 -10)" fill="url(#paint2_linear_9323_560)"/>
<path d="M121 937V1046" stroke="url(#paint3_linear_9323_560)"/>
<path d="M142 1133L142 1209" stroke="url(#paint4_linear_9323_560)"/>
<path opacity="0.7" d="M50 857L50 942" stroke="url(#paint5_linear_9323_560)"/>
<path d="M209 1014L209 1086" stroke="url(#paint6_linear_9323_560)"/>
<path d="M184 894L184 966" stroke="url(#paint7_linear_9323_560)"/>
<path d="M78 1069L78 1122" stroke="url(#paint8_linear_9323_560)"/>
<path opacity="0.4" d="M175 1096L175 1140" stroke="url(#paint9_linear_9323_560)"/>
<path opacity="0.2" d="M56 978L56 1029" stroke="url(#paint10_linear_9323_560)"/>
<path opacity="0.7" d="M919 36L919 121" stroke="url(#paint11_linear_9323_560)"/>
<path d="M925 264L925 317" stroke="url(#paint12_linear_9323_560)"/>
<path opacity="0.2" d="M903 173L903 224" stroke="url(#paint13_linear_9323_560)"/>
</g>
<defs>
<filter id="filter0_f_9323_560" x="216" y="-717" width="1769" height="1953" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_9323_560"/>
</filter>
<filter id="filter1_f_9323_560" x="-941" y="-157" width="1714" height="1877" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_9323_560"/>
</filter>
<linearGradient id="paint0_linear_9323_560" x1="462.5" y1="0" x2="462.5" y2="1209" gradientUnits="userSpaceOnUse">
<stop stop-color="#EAF2FF"/>
<stop offset="1" stop-color="#FFF3F6"/>
</linearGradient>
<linearGradient id="paint1_linear_9323_560" x1="894.882" y1="297" x2="894.882" y2="824.655" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.5" stop-color="white" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_9323_560" x1="466.492" y1="-10" x2="466.492" y2="568.872" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="0.5" stop-color="white" stop-opacity="0.5"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint3_linear_9323_560" x1="121.5" y1="937" x2="121.5" y2="1046" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint4_linear_9323_560" x1="142.5" y1="1133" x2="142.5" y2="1209" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint5_linear_9323_560" x1="50.5" y1="857" x2="50.5" y2="942" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint6_linear_9323_560" x1="209.5" y1="1014" x2="209.5" y2="1086" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint7_linear_9323_560" x1="184.5" y1="894" x2="184.5" y2="966" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint8_linear_9323_560" x1="78.5" y1="1069" x2="78.5" y2="1122" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_9323_560" x1="175.5" y1="1096" x2="175.5" y2="1140" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_9323_560" x1="56.5" y1="978" x2="56.5" y2="1029" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_9323_560" x1="919.5" y1="36" x2="919.5" y2="121" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint12_linear_9323_560" x1="925.5" y1="264" x2="925.5" y2="317" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint13_linear_9323_560" x1="903.5" y1="173" x2="903.5" y2="224" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.958275" stop-color="white" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_9323_560">
<rect width="925" height="1209" fill="white"/>
</clipPath>
</defs>
</svg>
